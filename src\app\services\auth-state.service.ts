import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, fromEvent, merge } from 'rxjs';
import { filter, distinctUntilChanged, debounceTime } from 'rxjs/operators';
import { AuthStateChangeEvent } from '../types/route-config.types';
import { DynamicRouteService } from './dynamic-route.service';

@Injectable({
  providedIn: 'root'
})
export class AuthStateService {
  private readonly dynamicRouteService = inject(DynamicRouteService);

  private authStateSubject = new BehaviorSubject<AuthStateChangeEvent | null>(null);
  private currentUserId: string | null = null;
  private currentToken: string | null = null;
  private isInitialized = false;

  public readonly authState$ = this.authStateSubject.asObservable();

  constructor() {
    this.initializeAuthStateMonitoring();
  }

  /**
   * 初始化认证状态监听
   */
  private initializeAuthStateMonitoring(): void {
    if (this.isInitialized) {
      return;
    }

    // 监听localStorage变化（用于跨标签页同步）
    const storageEvents$ = fromEvent<StorageEvent>(window, 'storage').pipe(
      filter(event => event.key === 'auth_token' || event.key === 'user_id'),
      debounceTime(100) // 防抖，避免频繁触发
    );

    // 监听自定义认证事件
    const customEvents$ = fromEvent<CustomEvent>(window, 'auth-state-change');

    // 合并所有认证状态变化事件
    merge(storageEvents$, customEvents$).subscribe(() => {
      this.checkAuthStateChange();
    });

    // 初始检查
    this.checkAuthStateChange();
    this.isInitialized = true;
  }

  /**
   * 检查认证状态变化
   */
  private checkAuthStateChange(): void {
    const newUserId = this.getUserIdFromStorage();
    const newToken = this.getTokenFromStorage();

    // 检查用户切换
    if (this.currentUserId !== newUserId) {
      const oldUserId = this.currentUserId;
      this.currentUserId = newUserId;

      if (oldUserId && newUserId) {
        // 用户切换
        this.emitAuthStateChange({
          type: 'user-switch',
          userId: newUserId,
          token: newToken,
          timestamp: Date.now()
        });
      } else if (!oldUserId && newUserId) {
        // 用户登录
        this.emitAuthStateChange({
          type: 'login',
          userId: newUserId,
          token: newToken,
          timestamp: Date.now()
        });
      } else if (oldUserId && !newUserId) {
        // 用户登出
        this.emitAuthStateChange({
          type: 'logout',
          userId: oldUserId,
          timestamp: Date.now()
        });
      }
    }
    // 检查令牌刷新
    else if (this.currentToken !== newToken && newUserId) {
      this.currentToken = newToken;
      this.emitAuthStateChange({
        type: 'token-refresh',
        userId: newUserId,
        token: newToken,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 发出认证状态变化事件
   */
  private emitAuthStateChange(event: AuthStateChangeEvent): void {
    this.authStateSubject.next(event);
    
    // 根据事件类型决定是否重新加载路由
    if (this.shouldReloadRoutes(event)) {
      this.reloadRoutes(event);
    }
  }

  /**
   * 判断是否需要重新加载路由
   */
  private shouldReloadRoutes(event: AuthStateChangeEvent): boolean {
    switch (event.type) {
      case 'login':
      case 'logout':
      case 'user-switch':
        return true;
      case 'token-refresh':
        // 令牌刷新时，可以选择性重新加载路由
        // 这里可以根据业务需求调整
        return false;
      default:
        return false;
    }
  }

  /**
   * 重新加载路由
   */
  private async reloadRoutes(event: AuthStateChangeEvent): Promise<void> {
    try {
      console.log(`Reloading routes due to auth state change: ${event.type}`);
      await this.dynamicRouteService.refresh();
    } catch (error) {
      console.error('Failed to reload routes after auth state change:', error);
    }
  }

  /**
   * 从存储中获取用户ID
   */
  private getUserIdFromStorage(): string | null {
    try {
      return localStorage.getItem('user_id');
    } catch (error) {
      console.warn('Failed to get user ID from storage:', error);
      return null;
    }
  }

  /**
   * 从存储中获取令牌
   */
  private getTokenFromStorage(): string | null {
    try {
      return localStorage.getItem('auth_token');
    } catch (error) {
      console.warn('Failed to get token from storage:', error);
      return null;
    }
  }

  /**
   * 手动触发认证状态变化
   */
  triggerAuthStateChange(event: Omit<AuthStateChangeEvent, 'timestamp'>): void {
    this.emitAuthStateChange({
      ...event,
      timestamp: Date.now()
    });
  }

  /**
   * 设置用户登录状态
   */
  setUserLogin(userId: string, token: string): void {
    try {
      localStorage.setItem('user_id', userId);
      localStorage.setItem('auth_token', token);
      
      // 触发自定义事件，确保所有监听器都能收到通知
      window.dispatchEvent(new CustomEvent('auth-state-change', {
        detail: { type: 'login', userId, token }
      }));
    } catch (error) {
      console.error('Failed to set user login state:', error);
    }
  }

  /**
   * 设置用户登出状态
   */
  setUserLogout(): void {
    const userId = this.currentUserId;
    
    try {
      localStorage.removeItem('user_id');
      localStorage.removeItem('auth_token');
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('auth-state-change', {
        detail: { type: 'logout', userId }
      }));
    } catch (error) {
      console.error('Failed to set user logout state:', error);
    }
  }

  /**
   * 刷新令牌
   */
  refreshToken(newToken: string): void {
    const userId = this.currentUserId;
    
    if (!userId) {
      console.warn('Cannot refresh token: no user logged in');
      return;
    }

    try {
      localStorage.setItem('auth_token', newToken);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('auth-state-change', {
        detail: { type: 'token-refresh', userId, token: newToken }
      }));
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }
  }

  /**
   * 切换用户
   */
  switchUser(newUserId: string, newToken: string): void {
    try {
      localStorage.setItem('user_id', newUserId);
      localStorage.setItem('auth_token', newToken);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('auth-state-change', {
        detail: { type: 'user-switch', userId: newUserId, token: newToken }
      }));
    } catch (error) {
      console.error('Failed to switch user:', error);
    }
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId(): string | null {
    return this.currentUserId;
  }

  /**
   * 获取当前令牌
   */
  getCurrentToken(): string | null {
    return this.currentToken;
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return !!(this.currentUserId && this.currentToken);
  }

  /**
   * 监听特定类型的认证状态变化
   */
  onAuthStateChange(type?: AuthStateChangeEvent['type']): Observable<AuthStateChangeEvent> {
    return this.authState$.pipe(
      filter(event => event !== null),
      filter(event => !type || event!.type === type),
      distinctUntilChanged((prev, curr) => 
        prev?.type === curr?.type && 
        prev?.userId === curr?.userId && 
        prev?.token === curr?.token
      )
    ) as Observable<AuthStateChangeEvent>;
  }
}
