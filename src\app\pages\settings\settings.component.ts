import { Component } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [RouterLink, RouterOutlet],
  template: `
    <div class="settings-container">
      <header class="page-header">
        <h1>系统设置</h1>
        <a routerLink="/" class="btn btn-secondary">返回首页</a>
      </header>

      <div class="settings-content">
        <nav class="settings-nav">
          <a routerLink="general" routerLinkActive="active" class="nav-item">
            ⚙️ 常规设置
          </a>
          <a routerLink="security" routerLinkActive="active" class="nav-item">
            🔒 安全设置
          </a>
        </nav>

        <div class="settings-main">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .settings-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #ecf0f1;
    }

    .page-header h1 {
      color: #2c3e50;
      margin: 0;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #7f8c8d;
    }

    .settings-content {
      display: grid;
      grid-template-columns: 250px 1fr;
      gap: 30px;
    }

    .settings-nav {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      height: fit-content;
    }

    .nav-item {
      display: block;
      padding: 12px 16px;
      color: #7f8c8d;
      text-decoration: none;
      border-radius: 4px;
      margin-bottom: 8px;
      transition: all 0.3s ease;
    }

    .nav-item:hover {
      background-color: #f8f9fa;
      color: #2c3e50;
    }

    .nav-item.active {
      background-color: #3498db;
      color: white;
    }

    .settings-main {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      min-height: 400px;
    }

    @media (max-width: 768px) {
      .settings-content {
        grid-template-columns: 1fr;
      }
      
      .settings-nav {
        display: flex;
        overflow-x: auto;
        padding: 10px;
      }
      
      .nav-item {
        white-space: nowrap;
        margin-right: 8px;
        margin-bottom: 0;
      }
    }
  `]
})
export class SettingsComponent {}
