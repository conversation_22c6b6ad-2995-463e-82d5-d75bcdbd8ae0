import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [RouterLink],
  template: `
    <div class="products-container">
      <header class="page-header">
        <h1>产品管理</h1>
        <a routerLink="/" class="btn btn-secondary">返回首页</a>
      </header>

      <div class="products-content">
        <div class="products-grid">
          <div class="product-card">
            <div class="product-image">📱</div>
            <h3>智能手机</h3>
            <p class="product-price">¥2,999</p>
            <p class="product-description">最新款智能手机，配备先进的处理器和高清摄像头。</p>
            <div class="product-actions">
              <button class="btn btn-primary">编辑</button>
              <button class="btn btn-danger">删除</button>
            </div>
          </div>

          <div class="product-card">
            <div class="product-image">💻</div>
            <h3>笔记本电脑</h3>
            <p class="product-price">¥8,999</p>
            <p class="product-description">高性能笔记本电脑，适合办公和游戏使用。</p>
            <div class="product-actions">
              <button class="btn btn-primary">编辑</button>
              <button class="btn btn-danger">删除</button>
            </div>
          </div>

          <div class="product-card">
            <div class="product-image">🎧</div>
            <h3>无线耳机</h3>
            <p class="product-price">¥599</p>
            <p class="product-description">高品质无线耳机，提供卓越的音质体验。</p>
            <div class="product-actions">
              <button class="btn btn-primary">编辑</button>
              <button class="btn btn-danger">删除</button>
            </div>
          </div>

          <div class="product-card">
            <div class="product-image">⌚</div>
            <h3>智能手表</h3>
            <p class="product-price">¥1,299</p>
            <p class="product-description">多功能智能手表，健康监测和运动追踪。</p>
            <div class="product-actions">
              <button class="btn btn-primary">编辑</button>
              <button class="btn btn-danger">删除</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .products-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #ecf0f1;
    }

    .page-header h1 {
      color: #2c3e50;
      margin: 0;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-primary {
      background-color: #3498db;
      color: white;
    }

    .btn-danger {
      background-color: #e74c3c;
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .product-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .product-card:hover {
      transform: translateY(-5px);
    }

    .product-image {
      font-size: 3rem;
      text-align: center;
      margin-bottom: 15px;
    }

    .product-card h3 {
      color: #2c3e50;
      margin-bottom: 10px;
      text-align: center;
    }

    .product-price {
      font-size: 1.5rem;
      font-weight: bold;
      color: #e74c3c;
      text-align: center;
      margin-bottom: 10px;
    }

    .product-description {
      color: #7f8c8d;
      line-height: 1.6;
      margin-bottom: 20px;
      text-align: center;
    }

    .product-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
    }
  `]
})
export class ProductsComponent {}
