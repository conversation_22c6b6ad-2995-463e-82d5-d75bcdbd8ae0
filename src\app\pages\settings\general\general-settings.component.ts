import { Component } from '@angular/core';

@Component({
  selector: 'app-general-settings',
  standalone: true,
  template: `
    <div class="general-settings">
      <h2>常规设置</h2>
      
      <form class="settings-form">
        <div class="form-group">
          <label for="siteName">网站名称</label>
          <input type="text" id="siteName" value="Angular Admin" class="form-control">
        </div>

        <div class="form-group">
          <label for="siteDescription">网站描述</label>
          <textarea id="siteDescription" rows="3" class="form-control">
一个基于Angular的动态路由管理后台系统
          </textarea>
        </div>

        <div class="form-group">
          <label for="language">默认语言</label>
          <select id="language" class="form-control">
            <option value="zh-CN">简体中文</option>
            <option value="en-US">English</option>
            <option value="ja-JP">日本語</option>
          </select>
        </div>

        <div class="form-group">
          <label for="timezone">时区</label>
          <select id="timezone" class="form-control">
            <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
            <option value="America/New_York">America/New_York (UTC-5)</option>
            <option value="Europe/London">Europe/London (UTC+0)</option>
          </select>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" checked>
            启用邮件通知
          </label>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox">
            启用短信通知
          </label>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn btn-primary">保存设置</button>
          <button type="button" class="btn btn-secondary">重置</button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .general-settings h2 {
      color: #2c3e50;
      margin-bottom: 20px;
    }

    .settings-form {
      max-width: 600px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #2c3e50;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .checkbox-label input[type="checkbox"] {
      margin-right: 8px;
      width: auto;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      margin-top: 30px;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: #3498db;
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #7f8c8d;
    }
  `]
})
export class GeneralSettingsComponent {}
