import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [RouterLink],
  template: `
    <div class="reports-container">
      <header class="page-header">
        <h1>报表中心</h1>
        <a routerLink="/" class="btn btn-secondary">返回首页</a>
      </header>

      <div class="reports-content">
        <div class="reports-grid">
          <div class="report-card">
            <div class="report-icon">📊</div>
            <h3>用户统计报表</h3>
            <p>用户注册、活跃度、留存率等关键指标统计</p>
            <div class="report-stats">
              <div class="stat">
                <span class="stat-label">总用户数</span>
                <span class="stat-value">1,234</span>
              </div>
              <div class="stat">
                <span class="stat-label">活跃用户</span>
                <span class="stat-value">987</span>
              </div>
            </div>
            <button class="btn btn-primary">查看详情</button>
          </div>

          <div class="report-card">
            <div class="report-icon">💰</div>
            <h3>销售报表</h3>
            <p>销售额、订单量、转化率等业务核心数据</p>
            <div class="report-stats">
              <div class="stat">
                <span class="stat-label">总销售额</span>
                <span class="stat-value">¥123,456</span>
              </div>
              <div class="stat">
                <span class="stat-label">订单数</span>
                <span class="stat-value">456</span>
              </div>
            </div>
            <button class="btn btn-primary">查看详情</button>
          </div>

          <div class="report-card">
            <div class="report-icon">📈</div>
            <h3>流量分析</h3>
            <p>网站访问量、页面浏览、用户行为分析</p>
            <div class="report-stats">
              <div class="stat">
                <span class="stat-label">今日访问</span>
                <span class="stat-value">2,345</span>
              </div>
              <div class="stat">
                <span class="stat-label">页面浏览</span>
                <span class="stat-value">8,901</span>
              </div>
            </div>
            <button class="btn btn-primary">查看详情</button>
          </div>

          <div class="report-card">
            <div class="report-icon">🎯</div>
            <h3>营销效果</h3>
            <p>广告投放、推广活动、ROI分析报表</p>
            <div class="report-stats">
              <div class="stat">
                <span class="stat-label">广告点击</span>
                <span class="stat-value">1,567</span>
              </div>
              <div class="stat">
                <span class="stat-label">转化率</span>
                <span class="stat-value">12.3%</span>
              </div>
            </div>
            <button class="btn btn-primary">查看详情</button>
          </div>
        </div>

        <div class="quick-actions">
          <h3>快速操作</h3>
          <div class="actions-grid">
            <button class="action-btn">
              <span class="action-icon">📋</span>
              <span>生成月度报表</span>
            </button>
            <button class="action-btn">
              <span class="action-icon">📧</span>
              <span>邮件发送报表</span>
            </button>
            <button class="action-btn">
              <span class="action-icon">📥</span>
              <span>导出数据</span>
            </button>
            <button class="action-btn">
              <span class="action-icon">⚙️</span>
              <span>报表设置</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .reports-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #ecf0f1;
    }

    .page-header h1 {
      color: #2c3e50;
      margin: 0;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-primary {
      background-color: #3498db;
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .reports-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }

    .report-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .report-card:hover {
      transform: translateY(-5px);
    }

    .report-icon {
      font-size: 2.5rem;
      text-align: center;
      margin-bottom: 15px;
    }

    .report-card h3 {
      color: #2c3e50;
      margin-bottom: 10px;
      text-align: center;
    }

    .report-card p {
      color: #7f8c8d;
      text-align: center;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .report-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 20px;
    }

    .stat {
      text-align: center;
    }

    .stat-label {
      display: block;
      font-size: 0.8rem;
      color: #7f8c8d;
      margin-bottom: 5px;
    }

    .stat-value {
      display: block;
      font-size: 1.2rem;
      font-weight: bold;
      color: #2c3e50;
    }

    .quick-actions {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .quick-actions h3 {
      color: #2c3e50;
      margin-bottom: 20px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }

    .action-btn {
      display: flex;
      align-items: center;
      padding: 15px;
      background: #f8f9fa;
      border: 1px solid #ecf0f1;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: #e9ecef;
      transform: translateY(-2px);
    }

    .action-icon {
      font-size: 1.5rem;
      margin-right: 10px;
    }
  `]
})
export class ReportsComponent {}
