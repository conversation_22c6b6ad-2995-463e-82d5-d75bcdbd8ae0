import { Type } from '@angular/core';

/**
 * 动态路由配置接口
 */
export interface DynamicRouteConfig {
  /** 路由路径 */
  path: string;
  /** 组件名称或标识符 */
  component?: string;
  /** 路由标题 */
  title?: string;
  /** 路由数据 */
  data?: Record<string, any>;
  /** 子路由 */
  children?: DynamicRouteConfig[];
  /** 重定向路径 */
  redirectTo?: string;
  /** 路径匹配策略 */
  pathMatch?: 'full' | 'prefix';
  /** 路由守卫 */
  canActivate?: string[];
  /** 懒加载模块路径 */
  loadChildren?: string;
  /** 路由元数据 */
  meta?: RouteMetadata;
}

/**
 * 路由元数据
 */
export interface RouteMetadata {
  /** 是否需要认证 */
  requiresAuth?: boolean;
  /** 所需权限 */
  permissions?: string[];
  /** 菜单显示名称 */
  menuTitle?: string;
  /** 菜单图标 */
  icon?: string;
  /** 是否在菜单中隐藏 */
  hideInMenu?: boolean;
  /** 排序权重 */
  order?: number;
}

/**
 * 后端返回的路由配置响应
 */
export interface RouteConfigResponse {
  /** 路由配置列表 */
  routes: DynamicRouteConfig[];
  /** 配置版本号 */
  version: string;
  /** 缓存时间（秒） */
  cacheTime?: number;
  /** 组件映射配置 */
  componentMap?: ComponentMapping[];
}

/**
 * 组件映射配置
 */
export interface ComponentMapping {
  /** 组件标识符 */
  name: string;
  /** 组件导入路径 */
  importPath: string;
  /** 组件类名 */
  className: string;
  /** 是否懒加载 */
  lazy?: boolean;
}

/**
 * 路由缓存配置
 */
export interface RouteCacheConfig {
  /** 缓存的路由配置 */
  routes: DynamicRouteConfig[];
  /** 配置版本 */
  version: string;
  /** 缓存时间戳 */
  timestamp: number;
  /** 过期时间（毫秒） */
  expiresAt: number;
}

/**
 * 组件加载状态
 */
export enum ComponentLoadState {
  IDLE = 'idle',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

/**
 * 组件加载结果
 */
export interface ComponentLoadResult {
  /** 加载状态 */
  state: ComponentLoadState;
  /** 组件类型 */
  component?: Type<any>;
  /** 错误信息 */
  error?: string;
}

/**
 * 路由加载事件
 */
export interface RouteLoadEvent {
  /** 事件类型 */
  type: 'start' | 'success' | 'error' | 'cache-hit';
  /** 路由路径 */
  path?: string;
  /** 错误信息 */
  error?: string;
  /** 加载时间 */
  timestamp: number;
}

/**
 * 认证状态变更事件
 */
export interface AuthStateChangeEvent {
  /** 事件类型 */
  type: 'login' | 'logout' | 'token-refresh' | 'user-switch';
  /** 用户ID */
  userId?: string;
  /** 新令牌 */
  token?: string;
  /** 时间戳 */
  timestamp: number;
}
