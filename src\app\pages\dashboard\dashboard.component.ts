import { Component, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { AuthStateService } from '../../services/auth-state.service';
import { DynamicRouteService } from '../../services/dynamic-route.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [RouterLink],
  template: `
    <div class="dashboard-container">
      <header class="dashboard-header">
        <h1>管理后台</h1>
        <div class="user-actions">
          <button (click)="simulateLogin()" class="btn btn-success">模拟登录</button>
          <button (click)="simulateLogout()" class="btn btn-warning">模拟登出</button>
          <button (click)="refreshRoutes()" class="btn btn-info">刷新路由</button>
        </div>
      </header>

      <div class="dashboard-content">
        <div class="welcome-section">
          <h2>欢迎使用动态路由系统</h2>
          <p>这是一个完全动态的路由加载演示。路由配置从后端获取，支持实时更新。</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <h3>🚀 动态路由</h3>
            <p>运行时从后端获取路由配置，无需重新部署即可更新路由结构。</p>
          </div>

          <div class="feature-card">
            <h3>⚡ 延迟加载</h3>
            <p>组件按需加载，提升应用启动速度和运行性能。</p>
          </div>

          <div class="feature-card">
            <h3>🔄 智能缓存</h3>
            <p>路由配置智能缓存，减少网络请求，提升用户体验。</p>
          </div>

          <div class="feature-card">
            <h3>🔐 认证集成</h3>
            <p>监听认证状态变化，自动重新加载适合当前用户的路由。</p>
          </div>
        </div>

        <div class="navigation-section">
          <h3>快速导航</h3>
          <div class="nav-links">
            <a routerLink="/users" class="nav-link">用户管理</a>
            <a routerLink="/products" class="nav-link">产品管理</a>
            <a routerLink="/settings" class="nav-link">系统设置</a>
            <a routerLink="/reports" class="nav-link">报表中心</a>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #ecf0f1;
    }

    .dashboard-header h1 {
      color: #2c3e50;
      margin: 0;
    }

    .user-actions {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s ease;
    }

    .btn-success {
      background-color: #27ae60;
      color: white;
    }

    .btn-warning {
      background-color: #f39c12;
      color: white;
    }

    .btn-info {
      background-color: #3498db;
      color: white;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .welcome-section {
      text-align: center;
      margin-bottom: 40px;
    }

    .welcome-section h2 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .welcome-section p {
      color: #7f8c8d;
      font-size: 1.1rem;
      line-height: 1.6;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }

    .feature-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-5px);
    }

    .feature-card h3 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .feature-card p {
      color: #7f8c8d;
      line-height: 1.6;
    }

    .navigation-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
    }

    .navigation-section h3 {
      color: #2c3e50;
      margin-bottom: 15px;
    }

    .nav-links {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }

    .nav-link {
      padding: 10px 20px;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background-color 0.3s ease;
    }

    .nav-link:hover {
      background-color: #2980b9;
    }
  `]
})
export class DashboardComponent {
  private readonly authStateService = inject(AuthStateService);
  private readonly dynamicRouteService = inject(DynamicRouteService);

  simulateLogin(): void {
    const userId = 'user_' + Date.now();
    const token = 'token_' + Math.random().toString(36).substr(2, 9);
    
    this.authStateService.setUserLogin(userId, token);
    console.log('Simulated login:', { userId, token });
  }

  simulateLogout(): void {
    this.authStateService.setUserLogout();
    console.log('Simulated logout');
  }

  async refreshRoutes(): Promise<void> {
    try {
      await this.dynamicRouteService.refresh();
      console.log('Routes refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh routes:', error);
    }
  }
}
