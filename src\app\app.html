<!-- Angular Dynamic Routes Demo -->

<style>
  :host {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
      "Segoe UI Symbol";
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .app-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .app-header h1 {
    color: white;
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
  }

  .header-info {
    margin-top: 10px;
  }

  .version {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
  }

  .app-main {
    flex: 1;
    padding: 20px;
    background: white;
    margin: 20px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .app-footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 15px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .app-footer p {
    color: white;
    margin: 0;
    font-size: 0.9rem;
  }
</style>

<div class="app-container">
  <header class="app-header">
    <h1>🚀 Angular 动态路由系统</h1>
    <div class="header-info">
      <span class="version">v1.0.0</span>
    </div>
  </header>
  
  <main class="app-main">
    <router-outlet></router-outlet>
  </main>
  
  <footer class="app-footer">
    <p>&copy; 2024 Angular Dynamic Routes Demo. 完全动态的路由加载机制演示。</p>
  </footer>
</div>
