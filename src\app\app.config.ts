import { ApplicationConfig, provideBrowserGlobalErrorListeners, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptorsFromDi, HTTP_INTERCEPTORS } from '@angular/common/http';

import { routes } from './app.routes';
import { DynamicRouteService } from './services/dynamic-route.service';
import { AuthStateService } from './services/auth-state.service';
import { MockApiService } from './services/mock-api.service';

export const appConfig: ApplicationConfig = {
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes), // 初始为空路由，由DynamicRouteService动态添加
    provideHttpClient(withInterceptorsFromDi()), // 提供HttpClient

    // 动态路由服务
    DynamicRouteService,
    AuthStateService,

    // Mock API拦截器
    {
      provide: HTTP_INTERCEPTORS,
      useClass: MockApiService,
      multi: true
    }
  ]
};
