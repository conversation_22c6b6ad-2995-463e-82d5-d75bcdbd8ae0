import { Component, signal, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { DynamicRouteService } from './services/dynamic-route.service';
import { AuthStateService } from './services/auth-state.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit {
  protected readonly title = signal('angular-admin');

  private readonly dynamicRouteService = inject(DynamicRouteService);
  private readonly authStateService = inject(AuthStateService);

  async ngOnInit() {
    try {
      // 初始化认证状态监听
      // AuthStateService在构造函数中已经开始监听，这里只是确保服务被实例化
      this.authStateService.getCurrentUserId();

      // 初始化动态路由
      await this.dynamicRouteService.initialize();

      console.log('Dynamic routes initialized successfully');
    } catch (error) {
      console.error('Failed to initialize dynamic routes:', error);
      // 在生产环境中，可以选择加载默认路由或显示错误页面
    }
  }
}
