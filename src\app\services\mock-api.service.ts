import { Injectable } from '@angular/core';
import { HttpClient, HttpInterceptor, HttpRequest, HttpHandler, HttpResponse } from '@angular/common/http';
import { Observable, of, delay } from 'rxjs';
import { RouteConfigResponse, DynamicRouteConfig } from '../types/route-config.types';

@Injectable({
  providedIn: 'root'
})
export class MockApiService implements HttpInterceptor {

  /**
   * 模拟路由配置数据
   */
  private getMockRouteConfig(): RouteConfigResponse {
    const routes: DynamicRouteConfig[] = [
      {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        component: 'DashboardComponent',
        title: '管理后台',
        data: { breadcrumb: '首页' },
        meta: {
          requiresAuth: false,
          menuTitle: '首页',
          icon: '🏠',
          order: 1
        }
      },
      {
        path: 'users',
        component: 'UsersComponent',
        title: '用户管理',
        data: { breadcrumb: '用户管理' },
        meta: {
          requiresAuth: true,
          permissions: ['user:read'],
          menuTitle: '用户管理',
          icon: '👥',
          order: 2
        }
      },
      {
        path: 'products',
        component: 'ProductsComponent',
        title: '产品管理',
        data: { breadcrumb: '产品管理' },
        meta: {
          requiresAuth: true,
          permissions: ['product:read'],
          menuTitle: '产品管理',
          icon: '📦',
          order: 3
        }
      },
      {
        path: 'settings',
        component: 'SettingsComponent',
        title: '系统设置',
        data: { breadcrumb: '系统设置' },
        children: [
          {
            path: '',
            redirectTo: 'general',
            pathMatch: 'full'
          },
          {
            path: 'general',
            component: 'GeneralSettingsComponent',
            title: '常规设置',
            data: { breadcrumb: '常规设置' }
          },
          {
            path: 'security',
            component: 'SecuritySettingsComponent',
            title: '安全设置',
            data: { breadcrumb: '安全设置' }
          }
        ],
        meta: {
          requiresAuth: true,
          permissions: ['settings:read'],
          menuTitle: '系统设置',
          icon: '⚙️',
          order: 4
        }
      },
      {
        path: 'reports',
        component: 'ReportsComponent',
        title: '报表中心',
        data: { breadcrumb: '报表中心' },
        meta: {
          requiresAuth: true,
          permissions: ['report:read'],
          menuTitle: '报表中心',
          icon: '📊',
          order: 5
        }
      }
    ];

    return {
      routes,
      version: '1.0.0',
      cacheTime: 30 * 60 * 1000 // 30分钟
    };
  }

  /**
   * 根据用户状态获取不同的路由配置
   */
  private getRouteConfigByUser(): RouteConfigResponse {
    const isLoggedIn = !!localStorage.getItem('user_id');
    const baseConfig = this.getMockRouteConfig();

    if (!isLoggedIn) {
      // 未登录用户只能访问首页
      return {
        ...baseConfig,
        routes: baseConfig.routes.filter(route =>
          route.path === '' ||
          route.path === 'dashboard' ||
          !route.meta?.requiresAuth
        )
      };
    }

    // 已登录用户可以访问所有路由
    return baseConfig;
  }

  /**
   * HTTP拦截器实现
   */
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<any> {
    // 只拦截路由配置API请求
    if (req.url.includes('/api/routes/config')) {
      console.log('Mock API: Intercepting route config request');

      const mockResponse = this.getRouteConfigByUser();

      // 模拟网络延迟
      return of(new HttpResponse({
        status: 200,
        body: mockResponse
      })).pipe(delay(500)); // 500ms延迟
    }

    // 其他请求正常处理
    return next.handle(req);
  }
}

/**
 * 提供Mock API拦截器的工厂函数
 */
export function provideMockApi() {
  return {
    provide: HTTP_INTERCEPTORS,
    useClass: MockApiService,
    multi: true
  };
}

// 导入HTTP_INTERCEPTORS
import { HTTP_INTERCEPTORS } from '@angular/common/http';
