import { Component } from '@angular/core';

@Component({
  selector: 'app-error',
  standalone: true,
  template: `
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h2>加载失败</h2>
        <p>组件加载时发生错误，请稍后重试。</p>
        <button (click)="retry()" class="btn btn-primary">重试</button>
      </div>
    </div>
  `,
  styles: [`
    .error-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
      padding: 20px;
    }

    .error-content {
      text-align: center;
      max-width: 400px;
    }

    .error-icon {
      font-size: 3rem;
      margin-bottom: 20px;
    }

    h2 {
      color: #e74c3c;
      margin-bottom: 10px;
    }

    p {
      color: #7f8c8d;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      background-color: #3498db;
      color: white;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }

    .btn:hover {
      background-color: #2980b9;
    }
  `]
})
export class ErrorComponent {
  retry(): void {
    window.location.reload();
  }
}
