import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-users',
  standalone: true,
  imports: [RouterLink],
  template: `
    <div class="users-container">
      <header class="page-header">
        <h1>用户管理</h1>
        <a routerLink="/" class="btn btn-secondary">返回首页</a>
      </header>

      <div class="users-content">
        <div class="users-stats">
          <div class="stat-card">
            <h3>总用户数</h3>
            <div class="stat-number">1,234</div>
          </div>
          <div class="stat-card">
            <h3>活跃用户</h3>
            <div class="stat-number">987</div>
          </div>
          <div class="stat-card">
            <h3>新增用户</h3>
            <div class="stat-number">56</div>
          </div>
        </div>

        <div class="users-table">
          <h2>用户列表</h2>
          <table>
            <thead>
              <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>邮箱</th>
                <th>状态</th>
                <th>注册时间</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1</td>
                <td>张三</td>
                <td><EMAIL></td>
                <td><span class="status active">活跃</span></td>
                <td>2024-01-15</td>
              </tr>
              <tr>
                <td>2</td>
                <td>李四</td>
                <td><EMAIL></td>
                <td><span class="status inactive">非活跃</span></td>
                <td>2024-01-10</td>
              </tr>
              <tr>
                <td>3</td>
                <td>王五</td>
                <td><EMAIL></td>
                <td><span class="status active">活跃</span></td>
                <td>2024-01-20</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .users-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #ecf0f1;
    }

    .page-header h1 {
      color: #2c3e50;
      margin: 0;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #7f8c8d;
    }

    .users-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
    }

    .stat-card h3 {
      color: #7f8c8d;
      margin: 0 0 10px 0;
      font-size: 0.9rem;
      text-transform: uppercase;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: #2c3e50;
    }

    .users-table {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .users-table h2 {
      color: #2c3e50;
      margin-bottom: 20px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #ecf0f1;
    }

    th {
      background-color: #f8f9fa;
      color: #2c3e50;
      font-weight: 600;
    }

    tr:hover {
      background-color: #f8f9fa;
    }

    .status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .status.active {
      background-color: #d4edda;
      color: #155724;
    }

    .status.inactive {
      background-color: #f8d7da;
      color: #721c24;
    }
  `]
})
export class UsersComponent {}
