import { Component } from '@angular/core';

@Component({
  selector: 'app-security-settings',
  standalone: true,
  template: `
    <div class="security-settings">
      <h2>安全设置</h2>
      
      <div class="security-sections">
        <section class="security-section">
          <h3>密码策略</h3>
          <form class="settings-form">
            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" checked>
                要求密码包含大写字母
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" checked>
                要求密码包含小写字母
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" checked>
                要求密码包含数字
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox">
                要求密码包含特殊字符
              </label>
            </div>

            <div class="form-group">
              <label for="minLength">最小密码长度</label>
              <input type="number" id="minLength" value="8" min="6" max="32" class="form-control">
            </div>
          </form>
        </section>

        <section class="security-section">
          <h3>会话管理</h3>
          <form class="settings-form">
            <div class="form-group">
              <label for="sessionTimeout">会话超时时间（分钟）</label>
              <input type="number" id="sessionTimeout" value="30" min="5" max="480" class="form-control">
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" checked>
                启用记住登录状态
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox">
                强制单点登录
              </label>
            </div>
          </form>
        </section>

        <section class="security-section">
          <h3>访问控制</h3>
          <form class="settings-form">
            <div class="form-group">
              <label for="maxLoginAttempts">最大登录尝试次数</label>
              <input type="number" id="maxLoginAttempts" value="5" min="3" max="10" class="form-control">
            </div>

            <div class="form-group">
              <label for="lockoutDuration">账户锁定时间（分钟）</label>
              <input type="number" id="lockoutDuration" value="15" min="5" max="60" class="form-control">
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" checked>
                启用IP白名单
              </label>
            </div>
          </form>
        </section>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">保存设置</button>
        <button type="button" class="btn btn-secondary">重置</button>
      </div>
    </div>
  `,
  styles: [`
    .security-settings h2 {
      color: #2c3e50;
      margin-bottom: 20px;
    }

    .security-sections {
      display: grid;
      gap: 30px;
      margin-bottom: 30px;
    }

    .security-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #e74c3c;
    }

    .security-section h3 {
      color: #2c3e50;
      margin-bottom: 15px;
      font-size: 1.2rem;
    }

    .settings-form {
      max-width: 500px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #2c3e50;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .checkbox-label input[type="checkbox"] {
      margin-right: 8px;
      width: auto;
    }

    .form-actions {
      display: flex;
      gap: 10px;
      padding-top: 20px;
      border-top: 1px solid #ecf0f1;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background-color: #3498db;
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #7f8c8d;
    }
  `]
})
export class SecuritySettingsComponent {}
