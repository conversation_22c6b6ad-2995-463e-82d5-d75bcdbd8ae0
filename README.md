# Angular 动态路由系统

一个完全动态的 Angular 路由加载机制演示项目，支持运行时从后端获取路由配置、动态注册路由、延迟加载组件、智能缓存和认证状态监听。

## 🚀 核心特性

### 1. 完全动态路由
- ✅ 运行时从后端 API 获取完整的路由树
- ✅ 支持嵌套路由和子路由配置
- ✅ 动态注册路由，无需重新部署应用

### 2. 智能组件加载
- ✅ 延迟加载匹配组件
- ✅ 约定优于配置的组件查找机制（移除了组件映射，更易维护）
- ✅ 404 回退处理

### 3. 智能缓存机制
- ✅ 路由配置本地缓存
- ✅ 可配置的缓存过期时间
- ✅ 版本控制和缓存失效

### 4. 认证状态集成
- ✅ 监听令牌刷新事件
- ✅ 用户切换时自动重新加载路由
- ✅ 跨标签页状态同步

### 5. 生产级功能
- ✅ 错误重试机制
- ✅ 加载状态管理
- ✅ 完整的 TypeScript 类型支持
- ✅ Mock API 演示

## 📁 项目结构

```
src/app/
├── types/
│   └── route-config.types.ts      # 路由配置类型定义
├── services/
│   ├── dynamic-route.service.ts   # 动态路由核心服务
│   ├── component-loader.service.ts # 组件加载器（约定优于配置）
│   ├── auth-state.service.ts      # 认证状态监听
│   └── mock-api.service.ts        # Mock API 服务
├── components/
│   ├── not-found/                 # 404 组件
│   ├── loading/                   # 加载组件
│   └── error/                     # 错误组件
├── pages/
│   ├── dashboard/                 # 首页
│   ├── users/                     # 用户管理
│   ├── products/                  # 产品管理
│   ├── settings/                  # 系统设置
│   └── reports/                   # 报表中心
├── app.config.ts                  # 应用配置
├── app.routes.ts                  # 初始路由配置
└── app.ts                         # 主应用组件
```

## 🎯 使用方法

### 1. 启动项目
```bash
npm install
npm start
```

### 2. 体验动态路由
访问 `http://localhost:4200`，你将看到：
- 动态加载的管理后台界面
- 可以模拟登录/登出操作
- 观察路由配置的动态变化

### 3. 模拟认证状态变化
在首页点击：
- "模拟登录" - 触发用户登录事件
- "模拟登出" - 触发用户登出事件
- "刷新路由" - 手动刷新路由配置

## 🔄 工作流程

1. **应用启动**
   - 初始化认证状态监听
   - 检查缓存的路由配置
   - 从后端获取最新路由配置

2. **路由配置获取**
   - 发送 HTTP 请求到 `/api/routes/config`
   - Mock API 根据认证状态返回不同配置
   - 缓存配置到 localStorage

3. **组件动态加载（约定优于配置）**
   - 根据组件名称自动查找组件文件
   - 支持多种约定路径：
     - `../pages/{kebab-case-name}/{kebab-case-name}.component`
     - `../components/{kebab-case-name}/{kebab-case-name}.component`
     - `../shared/components/{kebab-case-name}/{kebab-case-name}.component`
     - `../features/{kebab-case-name}/{kebab-case-name}.component`
   - 错误时显示回退组件

4. **认证状态变化**
   - 监听 localStorage 变化
   - 检测用户登录/登出/切换
   - 自动重新获取路由配置

## 🛠️ 组件命名约定

为了支持自动组件发现，请遵循以下命名约定：

### 组件文件结构
```
src/app/pages/user-management/user-management.component.ts
src/app/pages/product-list/product-list.component.ts
src/app/components/data-table/data-table.component.ts
```

### 后端路由配置
```json
{
  "path": "users",
  "component": "UserManagementComponent",
  "title": "用户管理"
}
```

组件加载器会自动将 `UserManagementComponent` 转换为 `user-management` 并在约定路径中查找。

## ✅ 项目状态

- ✅ **编译成功** - 项目无 TypeScript 错误
- ✅ **构建成功** - 生产构建正常
- ✅ **移除组件映射** - 简化维护，使用约定优于配置
- ✅ **类型安全** - 完整的 TypeScript 类型支持
- ✅ **错误处理** - 完善的错误处理和回退机制

## 🔍 主要改进

1. **移除组件映射机制** - 不再需要手动配置组件映射，系统自动根据约定查找组件
2. **简化维护** - 添加新页面只需创建组件文件，无需额外配置
3. **类型安全** - 修复了所有 TypeScript 类型错误
4. **约定优于配置** - 遵循 Angular 最佳实践

## 🚀 下一步

项目已经可以正常运行，你可以：

1. 启动开发服务器：`npm start`
2. 访问 `http://localhost:4200` 体验动态路由
3. 根据需要添加新的页面组件
4. 集成真实的后端 API（移除 Mock API）

## 📝 技术栈

- Angular 20.1.1
- TypeScript
- RxJS
- 动态导入
- localStorage 缓存
- HTTP 拦截器
