import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [RouterLink],
  template: `
    <div class="not-found-container">
      <div class="not-found-content">
        <h1>404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
        <div class="actions">
          <a routerLink="/" class="btn btn-primary">返回首页</a>
          <button (click)="goBack()" class="btn btn-secondary">返回上页</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .not-found-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 60vh;
      padding: 20px;
    }

    .not-found-content {
      text-align: center;
      max-width: 500px;
    }

    h1 {
      font-size: 6rem;
      font-weight: bold;
      color: #e74c3c;
      margin: 0;
      line-height: 1;
    }

    h2 {
      font-size: 2rem;
      color: #2c3e50;
      margin: 20px 0 10px;
    }

    p {
      font-size: 1.1rem;
      color: #7f8c8d;
      margin-bottom: 30px;
      line-height: 1.6;
    }

    .actions {
      display: flex;
      gap: 15px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      font-size: 1rem;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-block;
    }

    .btn-primary {
      background-color: #3498db;
      color: white;
    }

    .btn-primary:hover {
      background-color: #2980b9;
    }

    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #7f8c8d;
    }
  `]
})
export class NotFoundComponent {
  goBack(): void {
    window.history.back();
  }
}
