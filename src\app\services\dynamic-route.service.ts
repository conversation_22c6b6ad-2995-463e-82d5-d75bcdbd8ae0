import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap, switchMap } from 'rxjs/operators';
import {
  DynamicRouteConfig,
  RouteConfigResponse,
  RouteCacheConfig,
  RouteLoadEvent,
  ComponentLoadState
} from '../types/route-config.types';
import { ComponentLoaderService } from './component-loader.service';

@Injectable({
  providedIn: 'root'
})
export class DynamicRouteService {
  private readonly http = inject(HttpClient);
  private readonly router = inject(Router);
  private readonly componentLoader = inject(ComponentLoaderService);

  private readonly CACHE_KEY = 'dynamic_routes_cache';
  private readonly DEFAULT_CACHE_TIME = 30 * 60 * 1000; // 30分钟

  private routesSubject = new BehaviorSubject<DynamicRouteConfig[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private eventsSubject = new BehaviorSubject<RouteLoadEvent | null>(null);

  public readonly routes$ = this.routesSubject.asObservable();
  public readonly loading$ = this.loadingSubject.asObservable();
  public readonly events$ = this.eventsSubject.asObservable();

  private currentVersion = '';
  private apiEndpoint = '/api/routes/config'; // 可配置的API端点

  /**
   * 设置API端点
   */
  setApiEndpoint(endpoint: string): void {
    this.apiEndpoint = endpoint;
  }

  /**
   * 初始化路由配置
   */
  async initialize(): Promise<void> {
    this.emitEvent({ type: 'start', timestamp: Date.now() });
    
    try {
      // 首先尝试从缓存加载
      const cachedConfig = this.getCachedConfig();
      if (cachedConfig && this.isCacheValid(cachedConfig)) {
        await this.applyRouteConfig(cachedConfig.routes);
        this.currentVersion = cachedConfig.version;
        this.emitEvent({ type: 'cache-hit', timestamp: Date.now() });
        return;
      }

      // 从后端获取最新配置
      await this.loadRoutesFromServer();
    } catch (error) {
      this.emitEvent({ 
        type: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now() 
      });
      throw error;
    }
  }

  /**
   * 强制刷新路由配置
   */
  async refresh(): Promise<void> {
    this.clearCache();
    await this.loadRoutesFromServer();
  }

  /**
   * 从服务器加载路由配置
   */
  private async loadRoutesFromServer(): Promise<void> {
    this.loadingSubject.next(true);

    try {
      const response = await this.http.get<RouteConfigResponse>(this.apiEndpoint)
        .pipe(
          catchError(error => {
            console.error('Failed to load routes from server:', error);
            return throwError(() => error);
          })
        )
        .toPromise();

      if (!response) {
        throw new Error('No response received from server');
      }

      // 缓存配置
      this.cacheConfig(response);
      
      // 应用路由配置
      await this.applyRouteConfig(response.routes);
      
      this.currentVersion = response.version;
      this.emitEvent({ type: 'success', timestamp: Date.now() });
    } finally {
      this.loadingSubject.next(false);
    }
  }

  /**
   * 应用路由配置
   */
  private async applyRouteConfig(routes: DynamicRouteConfig[]): Promise<void> {
    // 转换动态路由配置为Angular路由配置
    const angularRoutes = await this.convertToAngularRoutes(routes);
    
    // 重置路由配置
    this.router.resetConfig([
      ...angularRoutes,
      // 添加404回退路由
      { 
        path: '**', 
        component: await this.componentLoader.loadComponent('NotFoundComponent')
      }
    ]);

    this.routesSubject.next(routes);
  }

  /**
   * 转换为Angular路由配置
   */
  private async convertToAngularRoutes(routes: DynamicRouteConfig[]): Promise<any[]> {
    const convertedRoutes: any[] = [];

    for (const route of routes) {
      const angularRoute: any = {
        path: route.path,
        data: route.data || {},
        title: route.title
      };

      // 处理重定向
      if (route.redirectTo) {
        angularRoute.redirectTo = route.redirectTo;
        angularRoute.pathMatch = route.pathMatch || 'full';
      }
      // 处理组件
      else if (route.component) {
        angularRoute.component = await this.componentLoader.loadComponent(route.component);
      }
      // 处理懒加载
      else if (route.loadChildren) {
        angularRoute.loadChildren = () => import(route.loadChildren!);
      }

      // 处理子路由
      if (route.children && route.children.length > 0) {
        angularRoute.children = await this.convertToAngularRoutes(route.children);
      }

      // 处理路由守卫
      if (route.canActivate && route.canActivate.length > 0) {
        angularRoute.canActivate = route.canActivate.map(guard => 
          this.componentLoader.loadGuard(guard)
        );
      }

      convertedRoutes.push(angularRoute);
    }

    return convertedRoutes;
  }

  /**
   * 缓存配置
   */
  private cacheConfig(response: RouteConfigResponse): void {
    const cacheTime = response.cacheTime || this.DEFAULT_CACHE_TIME;
    const cacheConfig: RouteCacheConfig = {
      routes: response.routes,
      version: response.version,
      timestamp: Date.now(),
      expiresAt: Date.now() + cacheTime
    };

    try {
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheConfig));
    } catch (error) {
      console.warn('Failed to cache route config:', error);
    }
  }

  /**
   * 获取缓存配置
   */
  private getCachedConfig(): RouteCacheConfig | null {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.warn('Failed to parse cached route config:', error);
      return null;
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cache: RouteCacheConfig): boolean {
    return Date.now() < cache.expiresAt;
  }

  /**
   * 清除缓存
   */
  private clearCache(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY);
    } catch (error) {
      console.warn('Failed to clear route cache:', error);
    }
  }

  /**
   * 发出事件
   */
  private emitEvent(event: RouteLoadEvent): void {
    this.eventsSubject.next(event);
  }

  /**
   * 获取当前路由配置
   */
  getCurrentRoutes(): DynamicRouteConfig[] {
    return this.routesSubject.value;
  }

  /**
   * 获取当前版本
   */
  getCurrentVersion(): string {
    return this.currentVersion;
  }

  /**
   * 检查是否正在加载
   */
  isLoading(): boolean {
    return this.loadingSubject.value;
  }
}
