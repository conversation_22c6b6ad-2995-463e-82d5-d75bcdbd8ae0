import { Injectable, Type } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { ComponentLoadState } from '../types/route-config.types';

@Injectable({
  providedIn: 'root'
})
export class ComponentLoaderService {
  private componentCache = new Map<string, Type<any>>();
  private loadingStates = new Map<string, BehaviorSubject<ComponentLoadState>>();

  // 默认组件映射
  private readonly defaultComponents = new Map<string, () => Promise<Type<any>>>([
    ['NotFoundComponent', () => this.loadNotFoundComponent()],
    ['LoadingComponent', () => this.loadLoadingComponent()],
    ['ErrorComponent', () => this.loadErrorComponent()]
  ]);

  /**
   * 加载组件
   */
  async loadComponent(componentName: string): Promise<Type<any>> {
    // 检查缓存
    if (this.componentCache.has(componentName)) {
      return this.componentCache.get(componentName)!;
    }

    // 检查是否正在加载
    if (this.loadingStates.has(componentName)) {
      const state = this.loadingStates.get(componentName)!;
      if (state.value === ComponentLoadState.LOADING) {
        // 等待加载完成
        return new Promise((resolve, reject) => {
          const subscription = state.subscribe(newState => {
            if (newState === ComponentLoadState.LOADED) {
              subscription.unsubscribe();
              resolve(this.componentCache.get(componentName)!);
            } else if (newState === ComponentLoadState.ERROR) {
              subscription.unsubscribe();
              reject(new Error(`Failed to load component: ${componentName}`));
            }
          });
        });
      }
    }

    // 开始加载
    const loadingState = new BehaviorSubject<ComponentLoadState>(ComponentLoadState.LOADING);
    this.loadingStates.set(componentName, loadingState);

    try {
      const component = await this.doLoadComponent(componentName);
      this.componentCache.set(componentName, component);
      loadingState.next(ComponentLoadState.LOADED);
      return component;
    } catch (error) {
      loadingState.next(ComponentLoadState.ERROR);
      console.error(`Failed to load component ${componentName}:`, error);

      // 返回错误组件作为回退
      return this.loadErrorComponent();
    }
  }

  /**
   * 实际加载组件的逻辑
   */
  private async doLoadComponent(componentName: string): Promise<Type<any>> {
    // 检查默认组件
    if (this.defaultComponents.has(componentName)) {
      const loader = this.defaultComponents.get(componentName)!;
      return await loader();
    }

    // 尝试动态导入（约定优于配置）
    return await this.loadByConvention(componentName);
  }

  /**
   * 按约定加载组件
   */
  private async loadByConvention(componentName: string): Promise<Type<any>> {
    // 从组件名称中提取基础名称（移除Component后缀）
    const baseName = componentName.replace(/Component$/, '');
    const kebabName = this.kebabCase(baseName);

    // 尝试多种可能的路径
    const possiblePaths = [
      `../pages/${kebabName}/${kebabName}.component`,
      `../components/${kebabName}/${kebabName}.component`,
      `../shared/components/${kebabName}/${kebabName}.component`,
      `../features/${kebabName}/${kebabName}.component`
    ];

    console.log(`Loading component ${componentName}, trying paths:`, possiblePaths);

    for (const path of possiblePaths) {
      try {
        console.log(`Attempting to load: ${path}`);
        const module = await import(path);
        const component = module[componentName] || module.default;

        if (component) {
          console.log(`Successfully loaded ${componentName} from ${path}`);
          return component;
        }
      } catch (error) {
        console.log(`Failed to load from ${path}:`, error);
        // 继续尝试下一个路径
        continue;
      }
    }

    throw new Error(`Component ${componentName} not found in any conventional path`);
  }

  /**
   * 加载路由守卫
   */
  loadGuard(_guardName: string): any {
    // 这里可以实现守卫的动态加载逻辑
    // 暂时返回一个简单的守卫
    return () => true;
  }

  /**
   * 加载404组件
   */
  private async loadNotFoundComponent(): Promise<Type<any>> {
    try {
      const module = await import('../components/not-found/not-found.component');
      return module.NotFoundComponent;
    } catch (error) {
      // 如果找不到自定义404组件，使用简单的内联组件
      console.warn('NotFoundComponent not found, using fallback');
      const { Component } = await import('@angular/core');
      const { RouterLink } = await import('@angular/router');

      return Component({
        template: `
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h1 style="color: #e74c3c; margin-bottom: 20px;">404 - Page Not Found</h1>
            <p style="color: #7f8c8d; margin-bottom: 20px;">The requested page could not be found.</p>
            <a routerLink="/" style="color: #3498db; text-decoration: none; padding: 10px 20px; border: 1px solid #3498db; border-radius: 4px;">Go Home</a>
          </div>
        `,
        standalone: true,
        imports: [RouterLink]
      })(class FallbackNotFoundComponent { });
    }
  }

  /**
   * 加载加载中组件
   */
  private async loadLoadingComponent(): Promise<Type<any>> {
    try {
      const module = await import('../components/loading/loading.component');
      return module.LoadingComponent;
    } catch (error) {
      console.warn('LoadingComponent not found, using fallback');
      const { Component } = await import('@angular/core');

      return Component({
        template: `
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20px;"></div>
            <p style="color: #7f8c8d; margin: 0;">Loading...</p>
            <style>
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          </div>
        `,
        standalone: true
      })(class FallbackLoadingComponent { });
    }
  }

  /**
   * 加载错误组件
   */
  private async loadErrorComponent(): Promise<Type<any>> {
    try {
      const module = await import('../components/error/error.component');
      return module.ErrorComponent;
    } catch (error) {
      console.warn('ErrorComponent not found, using fallback');
      const { Component } = await import('@angular/core');

      return Component({
        template: `
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h1 style="color: #e74c3c; margin-bottom: 20px;">Error</h1>
            <p style="color: #7f8c8d; margin-bottom: 20px;">An error occurred while loading the component.</p>
            <button onclick="window.location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">Retry</button>
          </div>
        `,
        standalone: true
      })(class FallbackErrorComponent { });
    }
  }



  /**
   * 获取组件加载状态
   */
  getLoadingState(componentName: string): Observable<ComponentLoadState> {
    const state = this.loadingStates.get(componentName);
    return state ? state.asObservable() : of(ComponentLoadState.IDLE);
  }

  /**
   * 清除组件缓存
   */
  clearCache(): void {
    this.componentCache.clear();
    this.loadingStates.clear();
  }

  /**
   * 预加载组件
   */
  async preloadComponent(componentName: string): Promise<void> {
    try {
      await this.loadComponent(componentName);
    } catch (error) {
      console.warn(`Failed to preload component ${componentName}:`, error);
    }
  }

  /**
   * 转换为kebab-case
   */
  private kebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  }
}
