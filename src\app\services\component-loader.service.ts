import { Injectable, Type, inject, Injector, Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ComponentLoadResult, ComponentLoadState } from '../types/route-config.types';

@Injectable({
  providedIn: 'root'
})
export class ComponentLoaderService {
  private readonly injector = inject(Injector);

  private componentCache = new Map<string, Type<any>>();
  private loadingStates = new Map<string, BehaviorSubject<ComponentLoadState>>();

  // 默认组件映射
  private readonly defaultComponents = new Map<string, () => Promise<Type<any>>>([
    ['NotFoundComponent', () => this.loadNotFoundComponent()],
    ['LoadingComponent', () => this.loadLoadingComponent()],
    ['ErrorComponent', () => this.loadErrorComponent()]
  ]);

  /**
   * 加载组件
   */
  async loadComponent(componentName: string): Promise<Type<any>> {
    // 检查缓存
    if (this.componentCache.has(componentName)) {
      return this.componentCache.get(componentName)!;
    }

    // 检查是否正在加载
    if (this.loadingStates.has(componentName)) {
      const state = this.loadingStates.get(componentName)!;
      if (state.value === ComponentLoadState.LOADING) {
        // 等待加载完成
        return new Promise((resolve, reject) => {
          const subscription = state.subscribe(newState => {
            if (newState === ComponentLoadState.LOADED) {
              subscription.unsubscribe();
              resolve(this.componentCache.get(componentName)!);
            } else if (newState === ComponentLoadState.ERROR) {
              subscription.unsubscribe();
              reject(new Error(`Failed to load component: ${componentName}`));
            }
          });
        });
      }
    }

    // 开始加载
    const loadingState = new BehaviorSubject<ComponentLoadState>(ComponentLoadState.LOADING);
    this.loadingStates.set(componentName, loadingState);

    try {
      const component = await this.doLoadComponent(componentName);
      this.componentCache.set(componentName, component);
      loadingState.next(ComponentLoadState.LOADED);
      return component;
    } catch (error) {
      loadingState.next(ComponentLoadState.ERROR);
      console.error(`Failed to load component ${componentName}:`, error);

      // 返回错误组件作为回退
      return this.loadErrorComponent();
    }
  }

  /**
   * 实际加载组件的逻辑
   */
  private async doLoadComponent(componentName: string): Promise<Type<any>> {
    // 检查默认组件
    if (this.defaultComponents.has(componentName)) {
      const loader = this.defaultComponents.get(componentName)!;
      return await loader();
    }

    // 尝试动态导入（约定优于配置）
    return await this.loadByConvention(componentName);
  }

  /**
   * 按约定加载组件
   */
  private async loadByConvention(componentName: string): Promise<Type<any>> {
    // 尝试多种可能的路径
    const possiblePaths = [
      `../components/${this.kebabCase(componentName)}/${this.kebabCase(componentName)}.component`,
      `../pages/${this.kebabCase(componentName)}/${this.kebabCase(componentName)}.component`,
      `../shared/components/${this.kebabCase(componentName)}/${this.kebabCase(componentName)}.component`,
      `../features/${this.kebabCase(componentName)}/${this.kebabCase(componentName)}.component`
    ];

    for (const path of possiblePaths) {
      try {
        const module = await import(path);
        const component = module[componentName] || module.default;

        if (component) {
          return component;
        }
      } catch (error) {
        // 继续尝试下一个路径
        continue;
      }
    }

    throw new Error(`Component ${componentName} not found in any conventional path`);
  }

  /**
   * 加载路由守卫
   */
  loadGuard(_guardName: string): any {
    // 这里可以实现守卫的动态加载逻辑
    // 暂时返回一个简单的守卫
    return () => true;
  }

  /**
   * 加载404组件
   */
  private async loadNotFoundComponent(): Promise<Type<any>> {
    try {
      const module = await import('../components/not-found/not-found.component');
      return module.NotFoundComponent;
    } catch (error) {
      // 如果找不到自定义404组件，返回默认的
      return this.createDefaultNotFoundComponent();
    }
  }

  /**
   * 加载加载中组件
   */
  private async loadLoadingComponent(): Promise<Type<any>> {
    try {
      const module = await import('../components/loading/loading.component');
      return module.LoadingComponent;
    } catch (error) {
      return this.createDefaultLoadingComponent();
    }
  }

  /**
   * 加载错误组件
   */
  private async loadErrorComponent(): Promise<Type<any>> {
    try {
      const module = await import('../components/error/error.component');
      return module.ErrorComponent;
    } catch (error) {
      return this.createDefaultErrorComponent();
    }
  }

  /**
   * 创建默认404组件
   */
  private createDefaultNotFoundComponent(): Type<any> {
    @Component({
      template: `
        <div style="text-align: center; padding: 50px;">
          <h1>404 - Page Not Found</h1>
          <p>The requested page could not be found.</p>
          <a routerLink="/" style="color: #3498db; text-decoration: none;">Go Home</a>
        </div>
      `,
      standalone: true,
      imports: [RouterLink]
    })
    class DefaultNotFoundComponent { }

    return DefaultNotFoundComponent;
  }

  /**
   * 创建默认加载组件
   */
  private createDefaultLoadingComponent(): Type<any> {
    @Component({
      template: `
        <div style="text-align: center; padding: 50px;">
          <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
          <p style="margin-top: 20px; color: #7f8c8d;">Loading...</p>
          <style>
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </div>
      `,
      standalone: true
    })
    class DefaultLoadingComponent { }

    return DefaultLoadingComponent;
  }

  /**
   * 创建默认错误组件
   */
  private createDefaultErrorComponent(): Type<any> {
    @Component({
      template: `
        <div style="text-align: center; padding: 50px;">
          <h1 style="color: #e74c3c;">Error</h1>
          <p style="color: #7f8c8d;">An error occurred while loading the component.</p>
          <button onclick="window.location.reload()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">Retry</button>
        </div>
      `,
      standalone: true
    })
    class DefaultErrorComponent { }

    return DefaultErrorComponent;
  }

  /**
   * 获取组件加载状态
   */
  getLoadingState(componentName: string): Observable<ComponentLoadState> {
    const state = this.loadingStates.get(componentName);
    return state ? state.asObservable() : of(ComponentLoadState.IDLE);
  }

  /**
   * 清除组件缓存
   */
  clearCache(): void {
    this.componentCache.clear();
    this.loadingStates.clear();
  }

  /**
   * 预加载组件
   */
  async preloadComponent(componentName: string): Promise<void> {
    try {
      await this.loadComponent(componentName);
    } catch (error) {
      console.warn(`Failed to preload component ${componentName}:`, error);
    }
  }

  /**
   * 转换为kebab-case
   */
  private kebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  }
}
